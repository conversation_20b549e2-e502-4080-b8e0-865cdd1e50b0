<template>
  <div class="min-h-screen bg-gray-50 dark:bg-zinc-900">
    <div class="bg-white dark:bg-zinc-800 border-b border-gray-200 dark:border-zinc-700">
      <div class="max-w-4xl mx-auto px-6 py-16 text-center">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-zinc-100 mb-4">
          Help Center
        </h1>
        <p class="text-lg text-gray-600 dark:text-zinc-400 max-w-2xl mx-auto">
          Explore our EventaHub<span class="text-red-600 font-medium">Help Center</span> for answers to your questions and assistance with
          your event management experience.
        </p>
      </div>
    </div>

    <div class="max-w-4xl mx-auto px-6 -mt-8 relative z-10">
      <div class="bg-white dark:bg-zinc-800 shadow-lg border border-gray-200 dark:border-zinc-700 p-6">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Icon icon="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search your keyword"
            class="block w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-zinc-600 bg-gray-50 dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 placeholder-gray-500 dark:placeholder-zinc-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
            @keydown.enter="handleSearch"
          />
          <button
            v-if="searchQuery.trim()"
            @click="clearSearch"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-zinc-300"
          >
            <Icon icon="heroicons:x-mark" class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>



    <div v-if="showSearchResults && searchResults.length > 0" class="max-w-4xl mx-auto px-6 py-8">
      <div class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold text-gray-900 dark:text-zinc-100">
            Search Results ({{ searchResults.length }})
          </h2>
          <button
            @click="clearSearch"
            class="text-gray-500 hover:text-gray-700 dark:text-zinc-400 dark:hover:text-zinc-200"
          >
            <Icon icon="heroicons:x-mark" class="h-5 w-5" />
          </button>
        </div>

        <div class="space-y-4">
          <div
            v-for="result in searchResults"
            :key="`${result.category}-${result.index}`"
            class="border border-gray-200 dark:border-zinc-700 p-4 hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors cursor-pointer"
            @click="openCategory(result.category)"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-red-600 font-medium">{{ result.categoryTitle }}</span>
            </div>
            <h3 class="font-medium text-gray-900 dark:text-zinc-100 mb-2">
              {{ result.question }}
            </h3>
            <p class="text-gray-600 dark:text-zinc-400 text-sm line-clamp-2">
              {{ result.answer }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="showSearchResults && searchResults.length === 0" class="max-w-4xl mx-auto px-6 py-8">
      <div class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 p-8 text-center">
        <Icon icon="heroicons:magnifying-glass" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-zinc-100 mb-2">
          No results found
        </h3>
        <p class="text-gray-600 dark:text-zinc-400 mb-4">
          We couldn't find any FAQs matching "{{ searchQuery }}". Try different keywords or browse our categories below.
        </p>
        <button
          @click="clearSearch"
          class="inline-flex items-center px-4 py-2 bg-red-600 text-white font-medium hover:bg-red-700 transition-colors"
        >
          Clear Search
        </button>
      </div>
    </div>

    <div class="max-w-6xl mx-auto px-6 py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="bg-white dark:bg-zinc-800 p-8 text-center border border-gray-200 dark:border-zinc-700 hover:shadow-lg transition-shadow">
          <div class="w-16 h-16 mx-auto mb-6 bg-gray-100 dark:bg-zinc-700 flex items-center justify-center">
            <Icon icon="heroicons:book-open" class="h-8 w-8 text-gray-600 dark:text-zinc-400" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-zinc-100 mb-4">
            Getting Started
          </h3>
          <p class="text-gray-600 dark:text-zinc-400 mb-6 text-sm leading-relaxed">
            Learn the basics of setting up your profile and connecting with events.
          </p>
          <button
            @click="openCategory('getting-started')"
            class="inline-flex items-center text-red-600 hover:text-red-700 font-medium text-sm"
          >
            Learn More
            <Icon icon="heroicons:arrow-right" class="ml-2 h-4 w-4" />
          </button>
        </div>

        <div
          :class="[
            'p-8 text-center border hover:shadow-lg transition-all duration-300 cursor-pointer',
            selectedCategory === 'account-settings'
              ? 'bg-red-600 text-white border-red-600'
              : 'bg-white dark:bg-zinc-800 border-gray-200 dark:border-zinc-700'
          ]"
          @click="openCategory('account-settings')"
        >
          <div
            :class="[
              'w-16 h-16 mx-auto mb-6 flex items-center justify-center',
              selectedCategory === 'account-settings'
                ? 'bg-white/20'
                : 'bg-gray-100 dark:bg-zinc-700'
            ]"
          >
            <Icon
              icon="heroicons:cog-6-tooth"
              :class="[
                'h-8 w-8',
                selectedCategory === 'account-settings'
                  ? 'text-white'
                  : 'text-gray-600 dark:text-zinc-400'
              ]"
            />
          </div>
          <h3
            :class="[
              'text-xl font-semibold mb-4',
              selectedCategory === 'account-settings'
                ? 'text-white'
                : 'text-gray-900 dark:text-zinc-100'
            ]"
          >
            Account Settings
          </h3>
          <p
            :class="[
              'mb-6 text-sm leading-relaxed',
              selectedCategory === 'account-settings'
                ? 'text-red-100'
                : 'text-gray-600 dark:text-zinc-400'
            ]"
          >
            Customize your experience with account settings, privacy controls, and notifications.
          </p>
          <button
            class="inline-flex items-center font-medium text-sm hover:opacity-80 transition-opacity"
            :class="[
              selectedCategory === 'account-settings'
                ? 'text-white'
                : 'text-red-600 hover:text-red-700'
            ]"
          >
            Learn More
            <Icon icon="heroicons:arrow-right" class="ml-2 h-4 w-4" />
          </button>
        </div>

        <div
          :class="[
            'p-8 text-center border hover:shadow-lg transition-all duration-300 cursor-pointer',
            selectedCategory === 'troubleshooting'
              ? 'bg-red-600 text-white border-red-600'
              : 'bg-white dark:bg-zinc-800 border-gray-200 dark:border-zinc-700'
          ]"
          @click="openCategory('troubleshooting')"
        >
          <div
            :class="[
              'w-16 h-16 mx-auto mb-6 flex items-center justify-center',
              selectedCategory === 'troubleshooting'
                ? 'bg-white/20'
                : 'bg-gray-100 dark:bg-zinc-700'
            ]"
          >
            <Icon
              icon="heroicons:wrench-screwdriver"
              :class="[
                'h-8 w-8',
                selectedCategory === 'troubleshooting'
                  ? 'text-white'
                  : 'text-gray-600 dark:text-zinc-400'
              ]"
            />
          </div>
          <h3
            :class="[
              'text-xl font-semibold mb-4',
              selectedCategory === 'troubleshooting'
                ? 'text-white'
                : 'text-gray-900 dark:text-zinc-100'
            ]"
          >
            Troubleshooting
          </h3>
          <p
            :class="[
              'mb-6 text-sm leading-relaxed',
              selectedCategory === 'troubleshooting'
                ? 'text-red-100'
                : 'text-gray-600 dark:text-zinc-400'
            ]"
          >
            Resolve common issues and errors for seamless event management usage.
          </p>
          <button
            class="inline-flex items-center font-medium text-sm hover:opacity-80 transition-opacity"
            :class="[
              selectedCategory === 'troubleshooting'
                ? 'text-white'
                : 'text-red-600 hover:text-red-700'
            ]"
          >
            Learn More
            <Icon icon="heroicons:arrow-right" class="ml-2 h-4 w-4" />
          </button>
        </div>

        <div
          :class="[
            'p-8 text-center border hover:shadow-lg transition-all duration-300 cursor-pointer',
            selectedCategory === 'event-management'
              ? 'bg-red-600 text-white border-red-600'
              : 'bg-white dark:bg-zinc-800 border-gray-200 dark:border-zinc-700'
          ]"
          @click="openCategory('event-management')"
        >
          <div
            :class="[
              'w-16 h-16 mx-auto mb-6 flex items-center justify-center',
              selectedCategory === 'event-management'
                ? 'bg-white/20'
                : 'bg-gray-100 dark:bg-zinc-700'
            ]"
          >
            <Icon
              icon="heroicons:calendar-days"
              :class="[
                'h-8 w-8',
                selectedCategory === 'event-management'
                  ? 'text-white'
                  : 'text-gray-600 dark:text-zinc-400'
              ]"
            />
          </div>
          <h3
            :class="[
              'text-xl font-semibold mb-4',
              selectedCategory === 'event-management'
                ? 'text-white'
                : 'text-gray-900 dark:text-zinc-100'
            ]"
          >
            Event Management
          </h3>
          <p
            :class="[
              'mb-6 text-sm leading-relaxed',
              selectedCategory === 'event-management'
                ? 'text-red-100'
                : 'text-gray-600 dark:text-zinc-400'
            ]"
          >
            Express yourself by creating and managing your events with photos, tiers, and more.
          </p>
          <button
            class="inline-flex items-center font-medium text-sm hover:opacity-80 transition-opacity"
            :class="[
              selectedCategory === 'event-management'
                ? 'text-white'
                : 'text-red-600 hover:text-red-700'
            ]"
          >
            Learn More
            <Icon icon="heroicons:arrow-right" class="ml-2 h-4 w-4" />
          </button>
        </div>

        <div
          :class="[
            'p-8 text-center border hover:shadow-lg transition-all duration-300 cursor-pointer',
            selectedCategory === 'payments-tickets'
              ? 'bg-red-600 text-white border-red-600'
              : 'bg-white dark:bg-zinc-800 border-gray-200 dark:border-zinc-700'
          ]"
          @click="openCategory('payments-tickets')"
        >
          <div
            :class="[
              'w-16 h-16 mx-auto mb-6 flex items-center justify-center',
              selectedCategory === 'payments-tickets'
                ? 'bg-white/20'
                : 'bg-gray-100 dark:bg-zinc-700'
            ]"
          >
            <Icon
              icon="heroicons:credit-card"
              :class="[
                'h-8 w-8',
                selectedCategory === 'payments-tickets'
                  ? 'text-white'
                  : 'text-gray-600 dark:text-zinc-400'
              ]"
            />
          </div>
          <h3
            :class="[
              'text-xl font-semibold mb-4',
              selectedCategory === 'payments-tickets'
                ? 'text-white'
                : 'text-gray-900 dark:text-zinc-100'
            ]"
          >
            Payments & Tickets
          </h3>
          <p
            :class="[
              'mb-6 text-sm leading-relaxed',
              selectedCategory === 'payments-tickets'
                ? 'text-red-100'
                : 'text-gray-600 dark:text-zinc-400'
            ]"
          >
            Discover tips on purchasing tickets and managing your event bookings effectively.
          </p>
          <button
            class="inline-flex items-center font-medium text-sm hover:opacity-80 transition-opacity"
            :class="[
              selectedCategory === 'payments-tickets'
                ? 'text-white'
                : 'text-red-600 hover:text-red-700'
            ]"
          >
            Learn More
            <Icon icon="heroicons:arrow-right" class="ml-2 h-4 w-4" />
          </button>
        </div>

        <div
          :class="[
            'p-8 text-center border hover:shadow-lg transition-all duration-300 cursor-pointer',
            selectedCategory === 'community-support'
              ? 'bg-red-600 text-white border-red-600'
              : 'bg-white dark:bg-zinc-800 border-gray-200 dark:border-zinc-700'
          ]"
          @click="openCategory('community-support')"
        >
          <div
            :class="[
              'w-16 h-16 mx-auto mb-6 flex items-center justify-center',
              selectedCategory === 'community-support'
                ? 'bg-white/20'
                : 'bg-gray-100 dark:bg-zinc-700'
            ]"
          >
            <Icon
              icon="heroicons:users"
              :class="[
                'h-8 w-8',
                selectedCategory === 'community-support'
                  ? 'text-white'
                  : 'text-gray-600 dark:text-zinc-400'
              ]"
            />
          </div>
          <h3
            :class="[
              'text-xl font-semibold mb-4',
              selectedCategory === 'community-support'
                ? 'text-white'
                : 'text-gray-900 dark:text-zinc-100'
            ]"
          >
            Community & Support
          </h3>
          <p
            :class="[
              'mb-6 text-sm leading-relaxed',
              selectedCategory === 'community-support'
                ? 'text-red-100'
                : 'text-gray-600 dark:text-zinc-400'
            ]"
          >
            Discover tips on building and managing your event community network effectively.
          </p>
          <button
            class="inline-flex items-center font-medium text-sm hover:opacity-80 transition-opacity"
            :class="[
              selectedCategory === 'community-support'
                ? 'text-white'
                : 'text-red-600 hover:text-red-700'
            ]"
          >
            Learn More
            <Icon icon="heroicons:arrow-right" class="ml-2 h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <div v-if="selectedCategory" class="max-w-4xl mx-auto px-6 py-12">
      <div class="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 p-8">
        <div class="flex items-center justify-between mb-8">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-zinc-100">
            {{ getCategoryTitle(selectedCategory) }}
          </h2>
          <button
            @click="selectedCategory = null"
            class="text-gray-500 hover:text-gray-700 dark:text-zinc-400 dark:hover:text-zinc-200"
          >
            <Icon icon="heroicons:x-mark" class="h-6 w-6" />
          </button>
        </div>

        <div class="space-y-4">
          <div
            v-for="(faq, index) in getFilteredFAQs()"
            :key="index"
            class="border border-gray-200 dark:border-zinc-700"
          >
            <button
              @click="toggleFAQ(index)"
              class="w-full border-l-4 border-red-600 px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors"
            >
              <span class="font-medium text-gray-900 dark:text-zinc-100">
                {{ faq.question }}
              </span>
              <Icon
                icon="heroicons:chevron-down"
                :class="[
                  'h-5 w-5 text-gray-500 transition-transform',
                  openFAQs.includes(index) ? 'rotate-180' : ''
                ]"
              />
            </button>
            <div
              v-if="openFAQs.includes(index)"
              class="px-6 py-4 border-t border-gray-200 dark:border-zinc-700 bg-gray-50 dark:bg-zinc-700"
            >
              <p class="text-gray-700 dark:text-zinc-300 leading-relaxed">
                {{ faq.answer }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Personal Assistance Section -->
    <div class="bg-white dark:bg-zinc-800 border-t border-gray-200 dark:border-zinc-700">
      <div class="max-w-6xl mx-auto px-6 py-16">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 class="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-zinc-100 mb-6">
              Need Personal Assistance?
            </h2>
            <p class="text-gray-600 dark:text-zinc-400 mb-8 leading-relaxed">
              If you couldn't find the information you need, our support team is ready
              to assist you. Send us a message and our support team will quickly reply.
            </p>
            <button
              @click="openSupportTicket"
              class="inline-flex items-center px-6 py-3 bg-red-600 text-white font-medium hover:bg-red-700 transition-colors"
            >
              Send a Message
            </button>
          </div>
          <div class="flex justify-center lg:justify-end">
            <img src="@/assets/illustrations/contact.png" alt="contact-illustration"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Reactive data
const searchQuery = ref('')
const selectedCategory = ref<string | null>(null)
const openFAQs = ref<number[]>([])

// FAQ data organized by category
const faqData = {
  'getting-started': [
    {
      question: 'How do I create an account?',
      answer: 'To create an account, click the "Sign Up" button on our homepage and fill in your details. You\'ll need to verify your email address before you can start using the platform.'
    },
    {
      question: 'How do I find events?',
      answer: 'You can browse events on our Events page, use the search function to find specific events, or filter by category, location, and date to find events that interest you.'
    },
    {
      question: 'How do I become an event host?',
      answer: 'To become an event host, you need to upgrade to a Basic subscription or higher. Once upgraded, you\'ll automatically receive host privileges and access to the dashboard for creating and managing events.'
    },
    {
      question: 'What is email verification and why is it required?',
      answer: 'Email verification ensures the security of your account and prevents unauthorized access. You must verify your email before you can log in and start using the platform features.'
    }
  ],
  'account-settings': [
    {
      question: 'How do I update my profile information?',
      answer: 'You can update your profile information by going to "My Profile" in your account menu. Here you can change your personal details, profile picture, and account preferences.'
    },
    {
      question: 'I forgot my password. How do I reset it?',
      answer: 'Click "Forgot Password" on the login page and enter your email address. We\'ll send you a link to reset your password. Follow the instructions in the email to create a new password.'
    },
    {
      question: 'How do I change my currency preferences?',
      answer: 'Go to your profile settings and select your preferred currency. The system will automatically convert prices to your chosen currency when the auto-convert setting is enabled.'
    },
    {
      question: 'How do I manage my notification settings?',
      answer: 'In your account settings, you can customize which notifications you receive via email and in-app alerts. You can control notifications for events, payments, refunds, and more.'
    }
  ],
  'troubleshooting': [
    {
      question: 'I\'m having trouble with the website. What should I do?',
      answer: 'If you\'re experiencing technical issues, try refreshing the page or clearing your browser cache. If the problem persists, please contact our support team with details about the issue you\'re experiencing.'
    },
    {
      question: 'Is the platform mobile-friendly?',
      answer: 'Yes, EventaHub is fully responsive and works on all devices including smartphones and tablets. You can also download our mobile app for the best mobile experience.'
    },
    {
      question: 'Why can\'t I log in to my account?',
      answer: 'Make sure you\'ve verified your email address. Unverified users cannot log in. If you\'ve verified your email and still can\'t log in, check your password or try resetting it.'
    },
    {
      question: 'My payment failed. What should I do?',
      answer: 'Payment failures can occur due to insufficient funds, network issues, or incorrect payment details. Try again with correct information, or contact your mobile money provider if the issue persists.'
    }
  ],
  'event-management': [
    {
      question: 'How do I create an event?',
      answer: 'To create an event, log in to your account and navigate to your dashboard. Click "Create Event" and fill in all the required information including event details, venue, pricing, and promotional materials.'
    },
    {
      question: 'How do I manage my event tickets?',
      answer: 'You can manage your event tickets through your dashboard. Here you can view sales, check attendee lists, process refunds, and generate reports.'
    },
    {
      question: 'How do I set up ticket tiers for my event?',
      answer: 'When creating an event, you can add multiple ticket tiers with different prices, descriptions, and availability. Each tier can have its own refund policy and seating information.'
    },
    {
      question: 'Can I edit my event after publishing?',
      answer: 'Yes, you can edit most event details after publishing through your dashboard. However, some changes like ticket prices may affect existing bookings, so review carefully before making changes.'
    }
  ],
  'payments-tickets': [
    {
      question: 'How do I book tickets for an event?',
      answer: 'To book tickets, find the event you want to attend, select the number of tickets you need, and proceed to checkout. You\'ll need to be logged in to complete your booking.'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept mobile money payments through PayChangu gateway, including major operators like Airtel Money and TNM Mpamba. Payment options are displayed during checkout.'
    },
    {
      question: 'Can I cancel or refund my ticket?',
      answer: 'Refund policies vary by event organizer. Check the specific event\'s refund policy before booking. If eligible, you can request a refund through your ticket management page or contact the event organizer.'
    },
    {
      question: 'How do I download my ticket?',
      answer: 'After successful payment, you can download your ticket PDF from your profile under the "Tickets" section. You\'ll also receive an email with your ticket details.'
    },
    {
      question: 'What happens if my payment times out?',
      answer: 'If your payment times out, the transaction will be cancelled and you can try again. Make sure you have sufficient funds and complete the payment within the specified time limit.'
    }
  ],
  'community-support': [
    {
      question: 'How do I follow event creators?',
      answer: 'You can follow event creators by visiting their profile pages and clicking the "Follow" button. This helps you stay updated with their latest events and activities.'
    },
    {
      question: 'How do I rate and review events?',
      answer: 'After attending an event, you can leave a rating and review on the event page. Your feedback helps other users make informed decisions and helps event organizers improve.'
    },
    {
      question: 'Can I invite friends to events?',
      answer: 'Yes, event organizers can send invitations to specific users. You can also share event links with friends through social media or direct messaging.'
    },
    {
      question: 'How do I report inappropriate content or behavior?',
      answer: 'If you encounter inappropriate content or behavior, please contact our support team immediately with details. We take community safety seriously and will investigate all reports.'
    }
  ]
}

// Computed properties
const searchResults = computed(() => {
  if (!searchQuery.value.trim()) return []

  const query = searchQuery.value.toLowerCase()
  const results: Array<{
    category: string
    categoryTitle: string
    question: string
    answer: string
    index: number
  }> = []

  Object.entries(faqData).forEach(([categoryKey, faqs]) => {
    faqs.forEach((faq, index) => {
      if (
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
      ) {
        results.push({
          category: categoryKey,
          categoryTitle: getCategoryTitle(categoryKey),
          question: faq.question,
          answer: faq.answer,
          index
        })
      }
    })
  })

  return results
})

const showSearchResults = computed(() => {
  return searchQuery.value.trim().length > 0
})

// Methods
const handleSearch = () => {
  // Search is handled by computed property
  if (searchQuery.value.trim()) {
    // If there are search results, automatically open the first category
    if (searchResults.value.length > 0) {
      const firstResult = searchResults.value[0]
      selectedCategory.value = firstResult.category
      openFAQs.value = [firstResult.index]
    }
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  selectedCategory.value = null
  openFAQs.value = []
}

const openCategory = (category: string) => {
  selectedCategory.value = category
  openFAQs.value = []
  searchQuery.value = '' // Clear search when opening category
}

const getCategoryTitle = (category: string) => {
  const titles: Record<string, string> = {
    'getting-started': 'Getting Started',
    'account-settings': 'Account Settings',
    'troubleshooting': 'Troubleshooting',
    'event-management': 'Event Management',
    'payments-tickets': 'Payments & Tickets',
    'community-support': 'Community & Support'
  }
  return titles[category] || 'Help Topics'
}

const getFilteredFAQs = () => {
  if (!selectedCategory.value) return []

  const categoryFAQs = faqData[selectedCategory.value as keyof typeof faqData] || []

  // If there's a search query, filter the FAQs
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    return categoryFAQs.filter(faq =>
      faq.question.toLowerCase().includes(query) ||
      faq.answer.toLowerCase().includes(query)
    )
  }

  return categoryFAQs
}

const toggleFAQ = (index: number) => {
  const currentIndex = openFAQs.value.indexOf(index)
  if (currentIndex > -1) {
    openFAQs.value.splice(currentIndex, 1)
  } else {
    openFAQs.value.push(index)
  }
}

const openSupportTicket = () => {
  // Navigate to contact page or open support form
  navigateTo('/contact-us')
}

// SEO
useHead({
  title: 'Help Center - EventaHub Malawi | FAQs & Support',
  meta: [
    {
      name: 'description',
      content: 'Get help and support for using EventaHub Malawi. Find answers to frequently asked questions about booking events, creating events, managing your account, and event hosting.'
    },
    {
      name: 'keywords',
      content: 'EventaHub help, FAQ, support, event booking help, create events help, account management, troubleshooting'
    },
    {
      property: 'og:title',
      content: 'Help Center - EventaHub Malawi | FAQs & Support'
    },
    {
      property: 'og:description',
      content: 'Get help and support for using EventaHub Malawi. Find answers to frequently asked questions about booking and creating events.'
    },
    {
      property: 'og:type',
      content: 'website'
    },
    {
      name: 'twitter:card',
      content: 'summary_large_image'
    },
    {
      name: 'twitter:title',
      content: 'Help Center - EventaHub Malawi | FAQs & Support'
    },
    {
      name: 'twitter:description',
      content: 'Get help and support for using EventaHub Malawi. Find answers to frequently asked questions.'
    },
    {
      name: 'robots',
      content: 'index, follow'
    }
  ]
})
</script>
